package com.drna.shadcn.compose.component.chart

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.unit.dp
import com.drna.shadcn.compose.themes.shadcnColors
import ir.ehsannarmani.compose_charts.ColumnChart
import ir.ehsannarmani.compose_charts.models.Bars
import ir.ehsannarmani.compose_charts.models.BarProperties

/**
 * A Jetpack Compose wrapper for MPAndroidChart's BarChart, styled to match Shadcn UI.
 *
 * @param modifier The modifier to be applied to the chart.
 * @param data A list of [ChartEntry] objects to display in the chart.
 * @param descriptionText The description text for the chart (usually hidden for clean UI).
 * @param barLabel The label for the bar dataset.
 * @param initialVisibleDataCount The initial number of data points to be visible on the chart.
 * @Param colors [BarChartStyle] that will be used to resolve the colors used for this chart in
 * @param contentSize [BarChartContentSize] that will be used to resolve the content size used for this chart in
 */
@Composable
fun BarChart(
    modifier: Modifier = Modifier,
    data: List<ChartEntry>,
    descriptionText: String = "",
    barLabel: String = "Values",
    initialVisibleDataCount: Int? = null,
    colors: BarChartStyle = BarChartDefaults.colors(),
    contentSize: BarChartContentSize = BarChartDefaults.contentSize()
) {
    val barsData = remember(data, colors) {
        data.map { entry ->
            Bars(
                label = entry.label,
                values = listOf(
                    Bars.Data(
                        value = entry.y.toDouble(),
                        color = SolidColor(entry.color ?: colors.barColor)
                    )
                )
            )
        }
    }

    ColumnChart(
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp)
            .padding(8.dp),
        data = barsData,
        barProperties = BarProperties(
            spacing = contentSize.barWidth.dp,
            thickness = (contentSize.barWidth * 20).dp
        )
    )
}

data class BarChartStyle(
    val xAxisTextColor: Color,
    val yAxisTextColor: Color,
    val barColor: Color,
    val dataTextColor: Color,
)

data class BarChartContentSize(
    val barWidth: Float,
    val xAxisTextSize: Float,
    val yAxisTextSize: Float,
    val dataTextSize: Float
)

object BarChartDefaults {
    @Composable
    fun colors(): BarChartStyle {
        val colors = MaterialTheme.shadcnColors
        return BarChartStyle(
            xAxisTextColor = colors.foreground,
            yAxisTextColor = colors.foreground,
            barColor = colors.chart3,
            dataTextColor = colors.foreground,
        )
    }

    fun contentSize(): BarChartContentSize {
        return BarChartContentSize(
            barWidth = 0.6f,
            xAxisTextSize = 10f,
            yAxisTextSize = 10f,
            dataTextSize = 10f
        )
    }
}
