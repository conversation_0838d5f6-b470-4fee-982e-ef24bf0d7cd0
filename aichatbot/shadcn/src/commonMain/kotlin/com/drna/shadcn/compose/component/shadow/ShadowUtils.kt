package com.drna.shadcn.compose.component.shadow

import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * Data class representing a box shadow configuration
 */
data class BoxShadow(
    val offsetX: Dp = 0.dp,
    val offsetY: Dp = 0.dp,
    val blurRadius: Dp = 0.dp,
    val spread: Dp = 0.dp,
    val color: Color = Color.Gray
)

/**
 * Expected function to apply shadow effect across platforms
 */
expect fun Modifier.boxShadow(
    offsetX: Dp,
    offsetY: Dp,
    blurRadius: Dp,
    spread: Dp,
    color: Color
): Modifier

/**
 * Extension function to apply BoxShadow to a Modifier
 */
fun Modifier.boxShadow(shadow: BoxShadow): Modifier = boxShadow(
    offsetX = shadow.offsetX,
    offsetY = shadow.offsetY,
    blurRadius = shadow.blurRadius,
    spread = shadow.spread,
    color = shadow.color
)
