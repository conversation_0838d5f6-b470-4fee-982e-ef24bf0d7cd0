package com.drna.shadcn.compose.themes

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.drna.shadcn.compose.component.shadow.BoxShadow

object Shadows {
    // Shadow data class to hold elevation and color
    data class ShadowConfig(
        val elevation: Dp,
        val color: Color = Color.Black.copy(alpha = 0.05f)
    )

    // Shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05)
    val shadowXs = BoxShadow(
        offsetX = 0.dp,
        offsetY = 4.dp,
        blurRadius = 8.dp,
        spread = 4.dp,
        color = Color.Black.copy(alpha = 0.05f)
    )

    // Shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)
    val shadowSm = BoxShadow(
        offsetX = 0.dp,
        offsetY = 4.dp,
        blurRadius = 4.dp,
        spread = 4.dp,
        color = Color.Black.copy(alpha = 0.1f)
    )

    // Shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10)
    val shadow = BoxShadow(
        offsetX = 0.dp,
        offsetY = 4.dp,
        blurRadius = 8.dp,
        spread = 8.dp,
        color = Color.Black.copy(alpha = 0.1f)
    )

    // Shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10)
    val shadowMd = BoxShadow(
        offsetX = 0.dp,
        offsetY = 4.dp,
        blurRadius = 12.dp,
        spread = 8.dp,
        color = Color.Black.copy(alpha = 0.1f)
    )

    // Shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10)
    val shadowLg = BoxShadow(
        offsetX = 0.dp,
        offsetY = 4.dp,
        blurRadius = 16.dp,
        spread = 12.dp,
        color = Color.Black.copy(alpha = 0.1f)
    )

    // Shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10)
    val shadowXl = BoxShadow(
        offsetX = 0.dp,
        offsetY = 4.dp,
        blurRadius = 20.dp,
        spread = 12.dp,
        color = Color.Black.copy(alpha = 0.1f)
    )
}