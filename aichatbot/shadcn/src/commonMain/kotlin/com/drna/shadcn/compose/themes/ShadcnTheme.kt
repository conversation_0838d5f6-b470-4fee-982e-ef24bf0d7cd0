package com.drna.shadcn.compose.themes

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Typography
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.staticCompositionLocalOf

val LocalShadcnColors = staticCompositionLocalOf<ShadcnColors> { LightColors }
val LocalShadcnRadius = staticCompositionLocalOf<ShadcnRadius> { Radius }

/**
 * Provides [ShadcnColors] and [ShadcnRadius] through a [CompositionLocalProvider] to be used in Shadcn Compose components.
 * It also applies MaterialTheme with the provided or default Material colors and typography.
 * notes:
 * - Use MaterialTheme.colorScheme for Material Design components.
 * - Use MaterialTheme.shadcnColors for ShadCN-specific styling.
 * - Use MaterialTheme.radius for ShadCN-specific styling.
 *
 * @param isDarkTheme Whether the theme should be dark or light. Defaults to the system setting.
 * @param shadcnLightColors The [ShadcnColors] to be used for the light theme. Defaults to [LightColors].
 * @param shadcnDarkColors The [ShadcnColors] to be used for the dark theme. Defaults to [DarkColors].
 * @param materialLightColors The Material 3 [ColorScheme] to be used for the light theme. Defaults to [lightColorScheme].
 * @param materialDarkColors The Material 3 [ColorScheme] to be used for the dark theme. Defaults to [darkColorScheme].
 * @param shadcnRadius The [ShadcnRadius] to be used. Defaults to [Radius].
 * @param typography The Material 3 [Typography] to be used. Defaults to [DefaultTypography].
 * @param content The composable content to be themed.
 */
@Composable
fun ShadcnTheme(
    isDarkTheme: Boolean = isSystemInDarkTheme(),
    shadcnLightColors: ShadcnColors = LightColors,
    shadcnDarkColors: ShadcnColors = DarkColors,
    materialLightColors: ColorScheme = lightColorScheme(),
    materialDarkColors: ColorScheme = darkColorScheme(),
    shadcnRadius: ShadcnRadius = Radius,
    typography: Typography? = null,
    content: @Composable () -> Unit,
) {
    val colors = if (isDarkTheme) shadcnDarkColors else shadcnLightColors
    val materialColorScheme = if (isDarkTheme) materialDarkColors else materialLightColors
    CompositionLocalProvider(
        LocalShadcnColors provides colors,
        LocalShadcnRadius provides shadcnRadius
    ) {
        MaterialTheme(
            colorScheme = materialColorScheme,
            typography = typography ?: DefaultTypography,
            content = content
        )
    }
}

val MaterialTheme.shadcnColors: ShadcnColors
    @Composable
    @ReadOnlyComposable
    get() = LocalShadcnColors.current

val MaterialTheme.radius: ShadcnRadius
    @Composable
    @ReadOnlyComposable
    get() = LocalShadcnRadius.current