package com.drna.shadcn.compose.component.chart

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.unit.dp
import com.drna.shadcn.compose.themes.shadcnColors
import ir.ehsannarmani.compose_charts.LineChart as ComposeLineChart
import ir.ehsannarmani.compose_charts.models.DrawStyle
import ir.ehsannarmani.compose_charts.models.DotProperties
import ir.ehsannarmani.compose_charts.models.Line

enum class LineChartVariant {
    Line,
    Area
}

/**
 * A Jetpack Compose wrapper for MPAndroidChart's LineChart, styled to match Shadcn UI,
 * configured to display a single line chart.
 *
 * @param modifier The modifier to be applied to the chart.
 * @param data A list of [ChartEntry] objects to display in the chart.
 * @param descriptionText The description text for the chart (usually hidden for clean UI).
 * @param areaLabel The label for the area dataset.
 * @param initialVisibleDataCount The number of data points to display initially. If the total
 *  number of data points exceeds this, the chart will be horizontally scrollable.
 * @param drawCircles Whether to draw circles at data points.
 * @param drawValues Whether to draw values on top of the line.
 * @param lineChartVariant The variant of the line chart (either Line or Area).
 * @param colors [LineChartStyle] that will be used to resolve the colors used for this chart in
 * @param contentSize [LineChartContentSize] that will be used to resolve the content size used for this chart in
 */
@Composable
fun LineChart(
    modifier: Modifier = Modifier,
    data: List<ChartEntry>,
    descriptionText: String = "",
    areaLabel: String = "Values",
    initialVisibleDataCount: Int? = null,
    drawCircles: Boolean = true,
    drawValues: Boolean = true,
    lineChartVariant: LineChartVariant = LineChartVariant.Line,
    colors: LineChartStyle = LineChartDefaults.colors(),
    contentSize: LineChartContentSize = LineChartDefaults.contentSize()
) {
    val lineData = remember(data, colors, lineChartVariant, drawCircles, contentSize) {
        Line(
            label = areaLabel,
            values = data.map { it.y.toDouble() },
            color = SolidColor(colors.lineColor),
            firstGradientFillColor = if (lineChartVariant == LineChartVariant.Area) colors.areaColor.copy(
                alpha = 0.7f
            ) else Color.Transparent,
            secondGradientFillColor = if (lineChartVariant == LineChartVariant.Area) colors.areaColor.copy(
                alpha = 0.1f
            ) else Color.Transparent,
            gradientAnimationDelay = 0,
            drawStyle = when (lineChartVariant) {
                LineChartVariant.Area -> DrawStyle.Fill
                LineChartVariant.Line -> DrawStyle.Stroke(width = contentSize.lineWidth.dp)
            },
            curvedEdges = true,
            dotProperties = DotProperties(
                enabled = drawCircles,
                color = SolidColor(colors.circleColor),
                strokeWidth = 1.dp,
                radius = contentSize.circleRadius.dp,
                strokeColor = SolidColor(colors.circleColor)
            )
        )
    }

    ComposeLineChart(
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp)
            .padding(8.dp),
        data = listOf(lineData)
    )
}

data class LineChartStyle(
    val xAxisTextColor: Color,
    val yAxisTextColor: Color,
    val dataTextColor: Color,
    val lineColor: Color,
    val circleColor: Color,
    val areaColor: Color
)

data class LineChartContentSize(
    val lineWidth: Float,
    val xAxisTextSize: Float,
    val yAxisTextSize: Float,
    val circleRadius: Float,
    val dataTextSize: Float
)

object LineChartDefaults {
    @Composable
    fun colors(): LineChartStyle {
        val colors = MaterialTheme.shadcnColors
        return LineChartStyle(
            xAxisTextColor = colors.foreground,
            yAxisTextColor = colors.foreground,
            dataTextColor = colors.foreground,
            lineColor = colors.chart3,
            circleColor = colors.chart3,
            areaColor = colors.chart2
        )
    }

    fun contentSize(): LineChartContentSize {
        return LineChartContentSize(
            lineWidth = 2f,
            xAxisTextSize = 10f,
            yAxisTextSize = 10f,
            circleRadius = 4f,
            dataTextSize = 9f
        )
    }
}