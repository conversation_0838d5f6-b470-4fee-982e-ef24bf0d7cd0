package com.drna.shadcn.compose.component.chart

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.unit.dp
import com.drna.shadcn.compose.themes.shadcnColors
import ir.ehsannarmani.compose_charts.ColumnChart
import ir.ehsannarmani.compose_charts.models.Bars
import ir.ehsannarmani.compose_charts.models.BarProperties

/**
 * A Jetpack Compose wrapper for MPAndroidChart's BarChart, styled to match Shadcn UI.
 * Displays grouped datasets.
 *
 * @param modifier The modifier to be applied to the chart.
 * @param data A list of [GroupedChartEntry] objects, where each entry represents a group on the X-axis.
 * @param datasetLabels A list of labels for each dataset within a group. The size of this list
 * should match the `values` list size in [GroupedChartEntry].
 * @param descriptionText The description text for the chart (usually hidden for clean UI).
 *
 * Bar size calculation formula
 * (barWidth + barSpace) * number of bar in group + groupSpace = 1
 * (0.25 + 0.05) * 3 + 0.1 = 1.00 --> interval per "group"
 * ref: https://github.com/PhilJay/MPAndroidChart/blob/9c7275a0596a7ac0e50ca566e680f7f9d73607af/MPChartExample/src/main/java/com/xxmassdeveloper/mpchartexample/CombinedChartActivity.java#L162
 */
@Composable
fun GroupedBarChart(
    modifier: Modifier = Modifier,
    data: List<GroupedChartEntry>,
    datasetLabels: List<String>,
    descriptionText: String = "",
    initialVisibleDataCount: Int = 3,
    colors: GroupedBarChartStyle = GroupedBarChartDefaults.colors(),
    contentSize: GroupedBarChartContentSize = GroupedBarChartDefaults.contentSize()
) {
    val barsData = remember(data, datasetLabels, colors) {
        data.map { entry ->
            Bars(
                label = entry.label,
                values = entry.values.mapIndexed { index, value ->
                    val colorIndex = index % colors.chartColors.size
                    val barColor = if (entry.colors != null && index < entry.colors.size) {
                        entry.colors[index]
                    } else {
                        colors.chartColors[colorIndex]
                    }
                    Bars.Data(
                        value = value.toDouble(),
                        color = SolidColor(barColor)
                    )
                }
            )
        }
    }

    ColumnChart(
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp)
            .padding(8.dp),
        data = barsData,
        barProperties = BarProperties(
            spacing = contentSize.groupSpace.dp,
            thickness = (contentSize.barWidth * 20).dp
        )
    )
}

data class GroupedBarChartStyle(
    val xAxisTextColor: Color,
    val yAxisTextColor: Color,
    val chartColors: List<Color>,
    val dataTextColor: Color,
)

data class GroupedBarChartContentSize(
    val barWidth: Float,
    val groupSpace: Float,
    val barSpace: Float,
    val xAxisTextSize: Float,
    val yAxisTextSize: Float,
    val dataTextSize: Float
)

object GroupedBarChartDefaults {
    @Composable
    fun colors(): GroupedBarChartStyle {
        val colors = MaterialTheme.shadcnColors
        return GroupedBarChartStyle(
            xAxisTextColor = colors.foreground,
            yAxisTextColor = colors.foreground,
            chartColors = listOf(
                colors.chart1,
                colors.chart2,
                colors.chart3,
                colors.chart4,
                colors.chart5,
            ),
            dataTextColor = colors.foreground,
        )
    }

    fun contentSize(): GroupedBarChartContentSize {
        return GroupedBarChartContentSize(
            barWidth = 0.25f,
            groupSpace = 0.1f,
            barSpace = 0.05f,
            xAxisTextSize = 10f,
            yAxisTextSize = 10f,
            dataTextSize = 10f
        )
    }
}