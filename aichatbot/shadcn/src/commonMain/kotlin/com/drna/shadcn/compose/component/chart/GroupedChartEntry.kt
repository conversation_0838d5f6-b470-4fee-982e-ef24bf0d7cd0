package com.drna.shadcn.compose.component.chart

import androidx.compose.ui.graphics.Color

/**
 * Data class to represent a single group of bars on the X-axis.
 * @param label The label for this group (displayed on the x-axis).
 * @param values A list of Y-values, where each value corresponds to a dataset within the group.
 * @param colors Optional colors for each value in the group.
 */
data class GroupedChartEntry(
    val label: String,
    val values: List<Float>,
    val colors: List<Color>? = null
)