package com.drna.shadcn.compose.component

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ProvideTextStyle
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.drna.shadcn.compose.themes.radius
import androidx.compose.ui.window.Dialog as ComposeDialog
import com.drna.shadcn.compose.themes.shadcnColors

/**
 * A Jetpack Compose Dialog component inspired by Shadcn UI.
 * Displays a modal dialog with a title, description, and customizable footer.
 *
 * @param onDismissRequest Callback invoked when the user tries to dismiss the dialog (e.g., by tapping outside).
 * @param open Boolean state controlling the visibility of the dialog.
 * @param modifier The modifier to be applied to the dialog's content area.
 * @param title The composable content for the dialog's title.
 * @param description The composable content for the dialog's description.
 * @param footer The composable content for the dialog's footer (e.g., action buttons).
 */
@Composable
fun Dialog(
    onDismissRequest: () -> Unit,
    open: Boolean,
    modifier: Modifier = Modifier,
    title: @Composable () -> Unit,
    description: @Composable () -> Unit,
    footer: @Composable () -> Unit
) {
    val colors = MaterialTheme.shadcnColors
    val radius = MaterialTheme.radius

    if (open) {
        ComposeDialog(onDismissRequest = onDismissRequest) {
            Column(
                modifier = modifier
                    .fillMaxWidth()
                    .background(colors.background, RoundedCornerShape(radius.lg))
                    .border(1.dp, colors.border, RoundedCornerShape(radius.lg))
                    .padding(24.dp)
            ) {
                // Header (Title, Description, and Close Icon)
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    Column(modifier = Modifier.weight(1f)) { // Take available space for title/desc
                        ProvideTextStyle(
                            value = TextStyle(
                                color = colors.foreground,
                                fontSize = 18.sp,
                                fontWeight = FontWeight.SemiBold
                            )
                        ) {
                            title()
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                        ProvideTextStyle(
                            value = TextStyle(
                                color = colors.mutedForeground,
                                fontSize = 14.sp
                            )
                        ) {
                            description()
                        }
                    }

                    Box(
                        modifier = Modifier
                            .offset(y = (-12).dp),
                    ) {
                        Button(
                            onClick = onDismissRequest,
                            size = ButtonSize.Icon,
                            variant = ButtonVariant.Ghost,
                            modifier = Modifier
                                .width(24.dp)
                                .height(24.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "Close dialog",
                                tint = colors.mutedForeground
                            )
                        }
                    }
                }
                Spacer(modifier = Modifier.height(24.dp))

                // Footer (Actions)
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    footer()
                }
            }
        }
    }
}

/**
 * Composable for the title of a Dialog.
 * This should be used within the `title` slot of [Dialog].
 */
@Composable
fun DialogTitle(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Column(modifier = modifier) {
        content()
    }
}

/**
 * Composable for the description of a Dialog.
 * This should be used within the `description` slot of [Dialog].
 */
@Composable
fun DialogDescription(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Column(modifier = modifier) {
        content()
    }
}

/**
 * Composable for an action button within a ShadcnDialog's `footer` slot.
 * Typically used for the primary action (e.g., "Save changes").
 * Uses [Button] with `ButtonVariant.Default`.
 */
@Composable
fun DialogAction(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Button(onClick = onClick, modifier = modifier, variant = ButtonVariant.Default) {
        content()
    }
}

/**
 * Composable for a cancel button within a ShadcnDialog's `footer` slot.
 * Typically used for a secondary action (e.g., "Cancel").
 * Uses [Button] with `ButtonVariant.Outline`.
 */
@Composable
fun DialogCancel(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Button(onClick = onClick, modifier = modifier, variant = ButtonVariant.Outline) {
        content()
    }
}
