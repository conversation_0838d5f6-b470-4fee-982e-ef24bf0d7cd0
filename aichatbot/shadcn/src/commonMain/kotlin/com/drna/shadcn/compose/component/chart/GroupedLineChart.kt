package com.drna.shadcn.compose.component.chart

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.unit.dp
import com.drna.shadcn.compose.themes.shadcnColors
import ir.ehsannarmani.compose_charts.LineChart as ComposeLineChart
import ir.ehsannarmani.compose_charts.models.DrawStyle
import ir.ehsannarmani.compose_charts.models.DotProperties
import ir.ehsannarmani.compose_charts.models.Line

/**
 * A Jetpack Compose wrapper for MPAndroidChart's LineChart, styled to match Shadcn UI,
 * configured to display multiple overlaid line charts.
 *
 * @param modifier The modifier to be applied to the chart.
 * @param data A list of [GroupedChartEntry] objects, where each entry represents a group on the X-axis.
 * @param datasetLabels A list of labels for each dataset. The size of this list should match
 * the number of values in [GroupedChartEntry.values].
 * @param descriptionText The description text for the chart (usually hidden for clean UI).
 * @param initialVisibleDataCount The number of data groups to display initially.
 * @param drawCircles Whether to draw circles at data points.
 * @param drawValues Whether to draw values on top of the lines.
 * @param lineChartVariant The variant of the line chart (either Line or Area).
 * @param colors [GroupedLineChartStyle] that will be used to resolve the colors used for this chart in
 * @param contentSize [GroupedLineChartContentSize] that will be used to resolve the content size used for this chart in
 */
@Composable
fun GroupedLineChart(
    modifier: Modifier = Modifier,
    data: List<GroupedChartEntry>,
    datasetLabels: List<String>,
    descriptionText: String = "",
    initialVisibleDataCount: Int = 5,
    drawCircles: Boolean = true,
    drawValues: Boolean = true,
    lineChartVariant: LineChartVariant = LineChartVariant.Line,
    colors: GroupedLineChartStyle = GroupedChartDefaults.colors(),
    contentSize: GroupedLineChartContentSize = GroupedChartDefaults.contentSize()
) {
    val linesData = remember(data, datasetLabels, colors, lineChartVariant, drawCircles, contentSize) {
        datasetLabels.mapIndexed { i, label ->
            val lineColor = colors.chartColors[i % colors.chartColors.size]
            Line(
                label = label,
                values = data.map { it.values[i].toDouble() },
                color = SolidColor(lineColor),
                firstGradientFillColor = if (lineChartVariant == LineChartVariant.Area) lineColor.copy(
                    alpha = 0.7f
                ) else Color.Transparent,
                secondGradientFillColor = if (lineChartVariant == LineChartVariant.Area) lineColor.copy(
                    alpha = 0.1f
                ) else Color.Transparent,
                gradientAnimationDelay = 0,
                drawStyle = when (lineChartVariant) {
                    LineChartVariant.Area -> DrawStyle.Fill
                    LineChartVariant.Line -> DrawStyle.Stroke(width = contentSize.lineWidth.dp)
                },
                curvedEdges = true,
                dotProperties = DotProperties(
                    enabled = drawCircles,
                    color = SolidColor(lineColor),
                    strokeWidth = 1.dp,
                    radius = contentSize.circleRadius.dp,
                    strokeColor = SolidColor(lineColor)
                )
            )
        }
    }

    ComposeLineChart(
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp)
            .padding(8.dp),
        data = linesData
    )
}

data class GroupedLineChartStyle(
    val xAxisTextColor: Color,
    val yAxisTextColor: Color,
    val dataTextColor: Color,
    val chartColors: List<Color>,
    val circleColor: Color,
)

data class GroupedLineChartContentSize(
    val lineWidth: Float,
    val xAxisTextSize: Float,
    val yAxisTextSize: Float,
    val circleRadius: Float,
    val dataTextSize: Float
)

object GroupedChartDefaults {
    @Composable
    fun colors(): GroupedLineChartStyle {
        val colors = MaterialTheme.shadcnColors
        return GroupedLineChartStyle(
            xAxisTextColor = colors.foreground,
            yAxisTextColor = colors.foreground,
            dataTextColor = colors.foreground,
            chartColors = listOf(
                colors.chart1,
                colors.chart2,
                colors.chart3,
                colors.chart4,
                colors.chart5,
            ),
            circleColor = colors.chart3,
        )
    }

    fun contentSize(): GroupedLineChartContentSize {
        return GroupedLineChartContentSize(
            lineWidth = 2f,
            xAxisTextSize = 10f,
            yAxisTextSize = 10f,
            circleRadius = 4f,
            dataTextSize = 9f
        )
    }
}