package com.drna.shadcn.compose.component.chart

import androidx.compose.ui.graphics.Color

/**
 * Data class to represent an entry in the chart.
 * @param y The y-value (height of the bar or line point).
 * @param label The label for this entry (displayed on the x-axis).
 * @param color Optional color override for this specific entry.
 */
data class ChartEntry(
    val y: Float,
    val label: String,
    val color: Color? = null
)