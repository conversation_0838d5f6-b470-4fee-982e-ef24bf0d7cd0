package com.drna.shadcn.compose.component.sidebar

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.drna.shadcn.compose.themes.shadcnColors
import com.drna.shadcn.compose.component.Button
import com.drna.shadcn.compose.component.ButtonSize
import com.drna.shadcn.compose.component.ButtonVariant
import com.drna.shadcn.compose.themes.radius

/**
 * Main sidebar container
 */
@Composable
fun Sidebar(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    val sidebarState = LocalSidebarState.current

    Box(
        modifier = modifier
            .fillMaxHeight()
            .width(280.dp)
            .background(
                color = MaterialTheme.shadcnColors.sidebar,
                shape = if (sidebarState.isMobile) RoundedCornerShape(topEnd = 8.dp, bottomEnd = 8.dp) else RoundedCornerShape(0.dp)
            )
    ) {
        content()
    }
}

/**
 * Sidebar trigger button (hamburger menu)
 */
@Composable
fun SidebarTrigger(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit = {
        Icon(
            Icons.Default.Menu,
            contentDescription = "Toggle sidebar",
            tint = MaterialTheme.shadcnColors.sidebarForeground
        )
    }
) {
    val sidebarState = LocalSidebarState.current

    // Show trigger on mobile always, and on desktop when sidebar is closeable
    Button(
        onClick = sidebarState.toggleSidebar,
        modifier = modifier,
        size = ButtonSize.Icon,
        variant = ButtonVariant.Ghost
    ) {
        content()
    }
}

/**
 * Main content wrapper that adapts to sidebar
 */
@Composable
fun SidebarInset(
    modifier: Modifier = Modifier,
    sidebarContent: @Composable () -> Unit,
    content: @Composable () -> Unit
) {
    val sidebarState = LocalSidebarState.current

    if (sidebarState.isMobile) {
        // Mobile: Sidebar overlays the content with a backdrop
        Box(modifier = modifier.fillMaxSize()) {
            // Main content takes full space
            content()

            // Backdrop when sidebar is open
            if (sidebarState.isOpen) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.5f))
                        .clickable { sidebarState.closeSidebar() }
                        .zIndex(1f)
                )
            }

            // Animated sidebar overlay
            AnimatedVisibility(
                visible = sidebarState.isOpen,
                enter = slideInHorizontally(
                    initialOffsetX = { -it },
                    animationSpec = tween(300)
                ),
                exit = slideOutHorizontally(
                    targetOffsetX = { -it },
                    animationSpec = tween(300)
                ),
                modifier = Modifier.zIndex(2f)
            ) {
                Sidebar {
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .width(280.dp)
                            .background(
                                color = MaterialTheme.shadcnColors.sidebar,
                                shape = RoundedCornerShape(topEnd = 8.dp, bottomEnd = 8.dp)
                            )
                    ) {
                        sidebarContent()
                    }
                }
            }
        }
    } else {
        // Desktop: Adjust content area based on sidebar presence
        Box(
            modifier = modifier
                .fillMaxSize()
                .then(if (sidebarState.isOpen) Modifier.padding(start = 16.dp) else Modifier)
        ) {
            content()
        }
    }
}

/**
 * Complete sidebar layout wrapper
 */
@Composable
fun SidebarLayout(
    sidebarContent: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    val sidebarState = LocalSidebarState.current

    if (sidebarState.isMobile) {
        // Mobile layout: Overlay
        Box(modifier = modifier.fillMaxSize()) {
            // Main content
            content()

            // Backdrop
            if (sidebarState.isOpen) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.5f))
                        .clickable { sidebarState.closeSidebar() }
                        .zIndex(1f)
                )
            }

            // Animated sidebar
            AnimatedVisibility(
                visible = sidebarState.isOpen,
                enter = slideInHorizontally(
                    initialOffsetX = { -it },
                    animationSpec = tween(300)
                ),
                exit = slideOutHorizontally(
                    targetOffsetX = { -it },
                    animationSpec = tween(300)
                ),
                modifier = Modifier.zIndex(2f)
            ) {
                Sidebar {
                    sidebarContent()
                }
            }
        }
    } else {
        // Desktop layout: Side by side, sidebar can be hidden
        Row(modifier = modifier.fillMaxSize()) {
            // Sidebar - only show when open
            AnimatedVisibility(
                visible = sidebarState.isOpen,
            ) {
                Sidebar {
                    sidebarContent()
                }
            }

            // Main content - takes remaining space
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .weight(1f)
            ) {
                content()
            }
        }
    }
}

/**
 * Sidebar content components
 */
@Composable
fun SidebarContent(
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        content()
    }
}

@Composable
fun SidebarHeader(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(bottom = 16.dp)
    ) {
        content()
    }
}

@Composable
fun SidebarFooter(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 16.dp)
    ) {
        content()
    }
}

@Composable
fun SidebarGroup(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        content()
    }
}

@Composable
fun SidebarGroupLabel(
    text: String,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        modifier = modifier.padding(horizontal = 8.dp, vertical = 4.dp),
        fontSize = 12.sp,
        fontWeight = FontWeight.Medium,
        color = MaterialTheme.shadcnColors.mutedForeground
    )
}

@Composable
fun SidebarGroupContent(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        content()
    }
}

@Composable
fun SidebarMenu(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        content()
    }
}

@Composable
fun SidebarMenuItem(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(MaterialTheme.radius.md))
            .clickable { onClick() }
            .padding(horizontal = 12.dp, vertical = 8.dp)
    ) {
        content()
    }
}

@Composable
fun SidebarMenuButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isActive: Boolean = false
) {
    val sidebarState = LocalSidebarState.current
    val colors = MaterialTheme.shadcnColors
    val backgroundColor = if (isActive) colors.sidebarAccent else Color.Transparent
    val textColor = if (isActive) colors.sidebarAccentForeground else colors.sidebarForeground

    Box(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(MaterialTheme.radius.md))
            .background(backgroundColor)
            .clickable {
                onClick()
                // Auto-close sidebar on mobile after menu selection
                if (sidebarState.isMobile) {
                    sidebarState.closeSidebar()
                }
            }
            .padding(horizontal = 12.dp, vertical = 8.dp)
    ) {
        Text(
            text = text,
            color = textColor,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}