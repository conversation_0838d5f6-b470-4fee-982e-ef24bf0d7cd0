package com.drna.shadcn.compose.component.shadow

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.Dp
import org.jetbrains.skia.FilterBlurMode
import org.jetbrains.skia.MaskFilter

/**
 * JVM (Desktop) implementation of box shadow using Skia MaskFilter
 */
actual fun Modifier.boxShadow(
    offsetX: Dp,
    offsetY: Dp,
    blurRadius: Dp,
    spread: Dp,
    color: Color
): Modifier = this.drawBehind {
    drawIntoCanvas { canvas ->
        val paint = Paint()
        val skiaPaint = paint.asFrameworkPaint()
        val spreadPixel = spread.toPx()
        val leftPixel = (0f - spreadPixel) + offsetX.toPx()
        val topPixel = (0f - spreadPixel) + offsetY.toPx()
        val rightPixel = (size.width + spreadPixel)
        val bottomPixel = (size.height + spreadPixel)

        if (blurRadius.value > 0f) {
            skiaPaint.maskFilter = MaskFilter.makeBlur(
                FilterBlurMode.NORMAL,
                blurRadius.toPx()
            )
        }

        skiaPaint.color = color.toArgb()
        canvas.drawRoundRect(
            left = leftPixel,
            top = topPixel,
            right = rightPixel,
            bottom = bottomPixel,
            radiusX = 0f,
            radiusY = 0f,
            paint = paint
        )
    }
}
