package com.drna.shadcn.compose.component.shadow

import android.graphics.BlurMaskFilter
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.Dp

/**
 * Android implementation of box shadow using BlurMaskFilter
 */
actual fun Modifier.boxShadow(
    offsetX: Dp,
    offsetY: Dp,
    blurRadius: Dp,
    spread: Dp,
    color: Color
): Modifier = this.drawBehind {
    drawIntoCanvas { canvas ->
        val paint = Paint()
        val frameworkPaint = paint.asFrameworkPaint()
        val spreadPixel = spread.toPx()
        val leftPixel = (0f - spreadPixel) + offsetX.toPx()
        val topPixel = (0f - spreadPixel) + offsetY.toPx()
        val rightPixel = (size.width + spreadPixel)
        val bottomPixel = (size.height + spreadPixel)

        if (blurRadius.value > 0f) {
            frameworkPaint.maskFilter =
                BlurMaskFilter(blurRadius.toPx(), BlurMaskFilter.Blur.NORMAL)
        }

        frameworkPaint.color = color.toArgb()
        canvas.drawRoundRect(
            left = leftPixel,
            top = topPixel,
            right = rightPixel,
            bottom = bottomPixel,
            radiusX = 0f,
            radiusY = 0f,
            paint = paint
        )
    }
}
