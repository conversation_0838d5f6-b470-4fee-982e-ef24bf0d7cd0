package com.chatbot.data.database

import androidx.room.Room
import androidx.room.RoomDatabase
import platform.Foundation.NSDocumentDirectory
import platform.Foundation.NSFileManager
import platform.Foundation.NSURL
import platform.Foundation.NSUserDomainMask

/**
 * iOS平台的数据库构建器
 */
actual fun getDatabaseBuilder(): RoomDatabase.Builder<AppDatabase> {
    val documentDirectory: NSURL = NSFileManager.defaultManager.URLsForDirectory(
        directory = NSDocumentDirectory,
        inDomains = NSUserDomainMask
    ).first() as NSURL

    val dbFile = documentDirectory.URLByAppendingPathComponent("app_database.db")!!

    return Room.databaseBuilder<AppDatabase>(
        name = dbFile.path!!,
    )
}