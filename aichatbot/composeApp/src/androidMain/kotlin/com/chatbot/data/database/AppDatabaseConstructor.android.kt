package com.chatbot.data.database

import android.content.Context
import androidx.room.Room
import androidx.room.RoomDatabase

/**
 * Android平台的数据库构建器
 */
actual fun getDatabaseBuilder(): RoomDatabase.Builder<AppDatabase> {
    val context = androidContext
        ?: throw IllegalStateException("Android context not initialized")

    val appContext = context.applicationContext
    val dbFile = appContext.getDatabasePath("app_database.db")
    return Room.databaseBuilder<AppDatabase>(
        context = appContext,
        name = dbFile.absolutePath
    )
}

private var androidContext: Context? = null

/**
 * 设置Android上下文
 * 必须在应用启动时调用
 */
fun setAndroidContext(context: Context) {
    androidContext = context.applicationContext
}