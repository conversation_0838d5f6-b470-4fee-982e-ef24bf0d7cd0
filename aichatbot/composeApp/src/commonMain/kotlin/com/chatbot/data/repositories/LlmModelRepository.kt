package com.chatbot.data.repositories

import com.chatbot.core.utils.TimeUtils
import com.chatbot.data.database.common.Status.ACTIVE
import com.chatbot.data.database.common.Status.INACTIVE
import com.chatbot.data.database.dao.LlmModelDao
import com.chatbot.data.database.entities.LlmModel
import com.chatbot.data.database.entities.ModelCapability
import kotlinx.coroutines.flow.Flow
import kotlinx.serialization.json.Json

/**
 * LLM模型仓库
 *
 * 提供对LLM模型数据的高级访问接口，实现Repository模式
 * 封装数据访问逻辑，提供清晰的业务接口
 */
class LlmModelRepository(
    private val modelDao: LlmModelDao
) {

    private val json = Json { ignoreUnknownKeys = true }

    /**
     * 获取所有模型（响应式）
     */
    fun getAllModels(): Flow<List<LlmModel>> {
        return modelDao.getAllModels()
    }

    /**
     * 获取所有活跃的模型（响应式）
     */
    fun getActiveModels(): Flow<List<LlmModel>> {
        return modelDao.getActiveModels()
    }

    /**
     * 根据ID获取模型（响应式）
     */
    fun getModelById(id: Long): Flow<LlmModel?> {
        return modelDao.getModelById(id)
    }

    /**
     * 根据ID获取模型（一次性）
     */
    suspend fun getModelByIdSync(id: Long): LlmModel? {
        return modelDao.getModelByIdSync(id)
    }

    /**
     * 根据提供商ID获取该提供商的所有模型
     */
    fun getModelsByProvider(providerId: Long): Flow<List<LlmModel>> {
        return modelDao.getModelsByProvider(providerId)
    }

    /**
     * 根据提供商ID获取该提供商的活跃模型
     */
    fun getActiveModelsByProvider(providerId: Long): Flow<List<LlmModel>> {
        return modelDao.getActiveModelsByProvider(providerId)
    }

    /**
     * 根据模型家族获取模型列表
     */
    fun getModelsByFamily(family: String): Flow<List<LlmModel>> {
        return modelDao.getModelsByFamily(family)
    }

    /**
     * 搜索模型
     */
    fun searchModels(query: String): Flow<List<LlmModel>> {
        return modelDao.searchModels(query)
    }

    /**
     * 根据能力搜索模型
     */
    fun getModelsByCapability(capability: String): Flow<List<LlmModel>> {
        return modelDao.getModelsByCapability(capability)
    }

    /**
     * 根据价格范围获取模型
     */
    fun getModelsByPriceRange(minPrice: Double, maxPrice: Double): Flow<List<LlmModel>> {
        return modelDao.getModelsByPriceRange(minPrice, maxPrice)
    }

    /**
     * 获取有定价信息的模型
     */
    fun getModelsWithPricing(): Flow<List<LlmModel>> {
        return modelDao.getModelsWithPricing()
    }

    /**
     * 获取指定Token限制范围内的模型
     */
    fun getModelsByTokenRange(minTokens: Int, maxTokens: Int): Flow<List<LlmModel>> {
        return modelDao.getModelsByTokenRange(minTokens, maxTokens)
    }

    /**
     * 创建新的模型
     */
    suspend fun createModel(
        providerId: Long,
        modelId: String,
        version: String? = null,
        description: String? = null,
        family: String? = null,
        releaseDate: String? = null,
        capabilities: List<String> = emptyList(),
        inputTokens: Double? = null,
        outputTokens: Double? = null,
        totalTokens: Double? = null,
        currency: String = "USD",
        pricingTier: String? = null,
        maxTokens: Int? = null,
        maxInputTokens: Int? = null,
        maxOutputTokens: Int? = null,
        supportedLanguages: List<String>? = null
    ): Result<Long> {
        return try {
            // 检查模型ID是否已存在
            if (isModelIdExists(modelId, providerId)) {
                return Result.failure(Exception("模型ID在该提供商下已存在"))
            }

            // 验证能力列表
            val validCapabilities = capabilities.filter {
                ModelCapability.isValidCapability(it)
            }

            val model = LlmModel(
                providerId = providerId,
                modelId = modelId,
                version = version,
                description = description,
                family = family,
                status = ACTIVE,
                releaseDate = releaseDate,
                capabilities = json.encodeToString(validCapabilities),
                inputTokens = inputTokens,
                outputTokens = outputTokens,
                totalTokens = totalTokens,
                currency = currency,
                pricingTier = pricingTier,
                maxTokens = maxTokens,
                maxInputTokens = maxInputTokens,
                maxOutputTokens = maxOutputTokens,
                supportedLanguages = supportedLanguages?.let { json.encodeToString(it) },
                createdAt = TimeUtils.currentTimestamp(),
                updatedAt = TimeUtils.currentTimestamp()
            )

            val id = modelDao.insertModel(model)
            Result.success(id)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 更新模型信息
     */
    suspend fun updateModel(
        id: Long,
        modelId: String? = null,
        version: String? = null,
        description: String? = null,
        family: String? = null,
        releaseDate: String? = null,
        capabilities: List<String>? = null,
        supportedLanguages: List<String>? = null
    ): Result<Unit> {
        return try {
            val existingModel = modelDao.getModelByIdSync(id)
                ?: return Result.failure(Exception("模型不存在"))

            // 如果模型ID有变化，检查新ID是否已存在
            if (modelId != null && modelId != existingModel.modelId) {
                if (isModelIdExists(modelId, existingModel.providerId, excludeId = id)) {
                    return Result.failure(Exception("模型ID在该提供商下已存在"))
                }
            }

            val updatedModel = existingModel.copy(
                modelId = modelId ?: existingModel.modelId,
                version = version ?: existingModel.version,
                description = description ?: existingModel.description,
                family = family ?: existingModel.family,
                releaseDate = releaseDate ?: existingModel.releaseDate,
                capabilities = capabilities?.let {
                    json.encodeToString(it.filter { cap ->
                        ModelCapability.isValidCapability(cap)
                    })
                } ?: existingModel.capabilities,
                supportedLanguages = supportedLanguages?.let {
                    json.encodeToString(it)
                } ?: existingModel.supportedLanguages,
                updatedAt = TimeUtils.currentTimestamp()
            )

            modelDao.updateModel(updatedModel)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 更新模型定价信息
     */
    suspend fun updateModelPricing(
        id: Long,
        inputTokens: Double? = null,
        outputTokens: Double? = null,
        totalTokens: Double? = null,
        currency: String? = null,
        pricingTier: String? = null
    ): Result<Unit> {
        return try {
            val existingModel = modelDao.getModelByIdSync(id)
                ?: return Result.failure(Exception("模型不存在"))

            modelDao.updateModelPricing(
                id = id,
                inputTokens = inputTokens ?: existingModel.inputTokens,
                outputTokens = outputTokens ?: existingModel.outputTokens,
                totalTokens = totalTokens ?: existingModel.totalTokens,
                currency = currency ?: existingModel.currency,
                pricingTier = pricingTier ?: existingModel.pricingTier,
                updatedAt = TimeUtils.currentTimestamp()
            )
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 更新模型Token限制
     */
    suspend fun updateModelTokenLimits(
        id: Long,
        maxTokens: Int? = null,
        maxInputTokens: Int? = null,
        maxOutputTokens: Int? = null
    ): Result<Unit> {
        return try {
            val existingModel = modelDao.getModelByIdSync(id)
                ?: return Result.failure(Exception("模型不存在"))

            modelDao.updateModelTokenLimits(
                id = id,
                maxTokens = maxTokens ?: existingModel.maxTokens,
                maxInputTokens = maxInputTokens ?: existingModel.maxInputTokens,
                maxOutputTokens = maxOutputTokens ?: existingModel.maxOutputTokens,
                updatedAt = TimeUtils.currentTimestamp()
            )
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 更新模型能力
     */
    suspend fun updateModelCapabilities(
        id: Long,
        capabilities: List<String>
    ): Result<Unit> {
        return try {
            val validCapabilities = capabilities.filter {
                ModelCapability.isValidCapability(it)
            }
            val capabilitiesJson = json.encodeToString(validCapabilities)

            modelDao.updateModelCapabilities(
                id = id,
                capabilities = capabilitiesJson,
                updatedAt = TimeUtils.currentTimestamp()
            )
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 激活模型
     */
    suspend fun activateModel(id: Long): Result<Unit> {
        return updateModelStatus(id, ACTIVE)
    }

    /**
     * 停用模型
     */
    suspend fun deactivateModel(id: Long): Result<Unit> {
        return updateModelStatus(id, INACTIVE)
    }

    /**
     * 更新模型状态
     */
    private suspend fun updateModelStatus(id: Long, status: Int): Result<Unit> {
        return try {
            modelDao.updateModelStatus(
                id = id,
                status = status,
                updatedAt = TimeUtils.currentTimestamp()
            )
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 删除模型
     */
    suspend fun deleteModel(id: Long): Result<Unit> {
        return try {
            modelDao.deleteModelById(id)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 批量删除模型
     */
    suspend fun deleteModels(ids: List<Long>): Result<Unit> {
        return try {
            modelDao.deleteModelsByIds(ids)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 根据提供商删除所有模型
     */
    suspend fun deleteModelsByProvider(providerId: Long): Result<Unit> {
        return try {
            modelDao.deleteModelsByProvider(providerId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 检查模型ID是否已存在
     */
    private suspend fun isModelIdExists(
        modelId: String,
        providerId: Long,
        excludeId: Long = -1
    ): Boolean {
        return modelDao.isModelIdExists(modelId, providerId, excludeId) > 0
    }

    /**
     * 获取模型数量统计
     */
    suspend fun getModelCount(): Int {
        return modelDao.getModelCount()
    }

    /**
     * 获取活跃模型数量
     */
    suspend fun getActiveModelCount(): Int {
        return modelDao.getActiveModelCount()
    }

    /**
     * 获取各提供商的模型数量统计
     */
    suspend fun getModelCountByProvider(): Map<Long, Int> {
        return modelDao.getModelCountByProvider()
    }

    /**
     * 获取各家族的模型数量统计
     */
    suspend fun getModelCountByFamily(): Map<String, Int> {
        return modelDao.getModelCountByFamily()
    }

    /**
     * 清空所有模型数据
     */
    suspend fun clearAllModels(): Result<Unit> {
        return try {
            modelDao.deleteAllModels()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

