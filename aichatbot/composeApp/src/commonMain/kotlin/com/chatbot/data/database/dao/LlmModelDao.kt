package com.chatbot.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.chatbot.data.database.common.Status
import com.chatbot.data.database.entities.LlmModel
import kotlinx.coroutines.flow.Flow

/**
 * LLM模型数据访问对象
 *
 * 提供对llm_models表的完整CRUD操作
 */
@Dao
interface LlmModelDao {

    /**
     * 获取所有模型（响应式）
     */
    @Query("SELECT * FROM llm_models ORDER BY created_at DESC")
    fun getAllModels(): Flow<List<LlmModel>>

    /**
     * 获取所有活跃的模型（响应式）
     */
    @Query("SELECT * FROM llm_models WHERE status = :status ORDER BY model_id ASC")
    fun getActiveModels(status: Int = Status.ACTIVE): Flow<List<LlmModel>>

    /**
     * 根据ID获取模型（响应式）
     */
    @Query("SELECT * FROM llm_models WHERE id = :id")
    fun getModelById(id: Long): Flow<LlmModel?>

    /**
     * 根据ID获取模型（一次性）
     */
    @Query("SELECT * FROM llm_models WHERE id = :id")
    suspend fun getModelByIdSync(id: Long): LlmModel?

    /**
     * 根据模型ID和提供商ID获取模型
     */
    @Query("SELECT * FROM llm_models WHERE model_id = :modelId AND provider_id = :providerId")
    suspend fun getModelByModelIdAndProvider(modelId: String, providerId: Long): LlmModel?

    /**
     * 根据提供商ID获取该提供商的所有模型
     */
    @Query("SELECT * FROM llm_models WHERE provider_id = :providerId ORDER BY model_id ASC")
    fun getModelsByProvider(providerId: Long): Flow<List<LlmModel>>

    /**
     * 根据提供商ID获取该提供商的活跃模型
     */
    @Query(
        """
        SELECT * FROM llm_models
        WHERE provider_id = :providerId AND status = :status
        ORDER BY model_id ASC
    """
    )
    fun getActiveModelsByProvider(
        providerId: Long, status: Int = Status.ACTIVE
    ): Flow<List<LlmModel>>

    /**
     * 根据模型家族获取模型列表
     */
    @Query("SELECT * FROM llm_models WHERE family = :family ORDER BY model_id ASC")
    fun getModelsByFamily(family: String): Flow<List<LlmModel>>

    /**
     * 搜索模型（按模型ID、描述或家族）
     */
    @Query(
        """
        SELECT * FROM llm_models
        WHERE model_id LIKE '%' || :query || '%'
           OR description LIKE '%' || :query || '%'
           OR family LIKE '%' || :query || '%'
        ORDER BY model_id ASC
    """
    )
    fun searchModels(query: String): Flow<List<LlmModel>>

    /**
     * 根据能力搜索模型
     */
    @Query(
        """
        SELECT * FROM llm_models
        WHERE capabilities LIKE '%' || :capability || '%'
           AND status = :status
        ORDER BY model_id ASC
    """
    )
    fun getModelsByCapability(
        capability: String, status: Int = Status.ACTIVE
    ): Flow<List<LlmModel>>

    /**
     * 根据价格范围获取模型
     */
    @Query(
        """
        SELECT * FROM llm_models
        WHERE ((input_tokens IS NOT NULL AND input_tokens BETWEEN :minPrice AND :maxPrice)
           OR (output_tokens IS NOT NULL AND output_tokens BETWEEN :minPrice AND :maxPrice)
           OR (total_tokens IS NOT NULL AND total_tokens BETWEEN :minPrice AND :maxPrice))
           AND status = :status
        ORDER BY COALESCE(input_tokens, output_tokens, total_tokens) ASC
    """
    )
    fun getModelsByPriceRange(
        minPrice: Double, maxPrice: Double, status: Int = Status.ACTIVE
    ): Flow<List<LlmModel>>

    /**
     * 插入模型
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertModel(model: LlmModel): Long

    /**
     * 批量插入模型
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertModels(models: List<LlmModel>): List<Long>

    /**
     * 更新模型
     */
    @Update
    suspend fun updateModel(model: LlmModel)

    /**
     * 批量更新模型
     */
    @Update
    suspend fun updateModels(models: List<LlmModel>)

    /**
     * 删除模型
     */
    @Delete
    suspend fun deleteModel(model: LlmModel)

    /**
     * 根据ID删除模型
     */
    @Query("DELETE FROM llm_models WHERE id = :id")
    suspend fun deleteModelById(id: Long)

    /**
     * 批量删除模型
     */
    @Query("DELETE FROM llm_models WHERE id IN (:ids)")
    suspend fun deleteModelsByIds(ids: List<Long>)

    /**
     * 根据提供商ID删除所有模型
     */
    @Query("DELETE FROM llm_models WHERE provider_id = :providerId")
    suspend fun deleteModelsByProvider(providerId: Long)

    /**
     * 更新模型状态
     */
    @Query("UPDATE llm_models SET status = :status, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateModelStatus(id: Long, status: Int, updatedAt: Long)

    /**
     * 更新模型定价信息
     */
    @Query(
        """
        UPDATE llm_models
        SET input_tokens = :inputTokens,
            output_tokens = :outputTokens,
            total_tokens = :totalTokens,
            currency = :currency,
            pricing_tier = :pricingTier,
            updated_at = :updatedAt
        WHERE id = :id
    """
    )
    suspend fun updateModelPricing(
        id: Long,
        inputTokens: Double?,
        outputTokens: Double?,
        totalTokens: Double?,
        currency: String,
        pricingTier: String?,
        updatedAt: Long
    )

    /**
     * 更新模型Token限制
     */
    @Query(
        """
        UPDATE llm_models
        SET max_tokens = :maxTokens,
            max_input_tokens = :maxInputTokens,
            max_output_tokens = :maxOutputTokens,
            updated_at = :updatedAt
        WHERE id = :id
    """
    )
    suspend fun updateModelTokenLimits(
        id: Long, maxTokens: Int?, maxInputTokens: Int?, maxOutputTokens: Int?, updatedAt: Long
    )

    /**
     * 更新模型能力
     */
    @Query("UPDATE llm_models SET capabilities = :capabilities, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateModelCapabilities(id: Long, capabilities: String, updatedAt: Long)

    /**
     * 更新模型支持的语言
     */
    @Query("UPDATE llm_models SET supported_languages = :supportedLanguages, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateModelSupportedLanguages(
        id: Long, supportedLanguages: String?, updatedAt: Long
    )

    /**
     * 检查模型ID是否已存在（在指定提供商下）
     */
    @Query(
        """
        SELECT COUNT(*) FROM llm_models
        WHERE model_id = :modelId AND provider_id = :providerId AND id != :excludeId
    """
    )
    suspend fun isModelIdExists(modelId: String, providerId: Long, excludeId: Long = -1): Int

    /**
     * 获取模型数量统计
     */
    @Query("SELECT COUNT(*) FROM llm_models")
    suspend fun getModelCount(): Int

    /**
     * 获取活跃模型数量
     */
    @Query("SELECT COUNT(*) FROM llm_models WHERE status = :status")
    suspend fun getActiveModelCount(status: Int = Status.ACTIVE): Int

    /**
     * 获取各提供商的模型数量统计
     */
    @Query(
        """
        SELECT provider_id, COUNT(*) as count
        FROM llm_models
        WHERE status = :status
        GROUP BY provider_id
        ORDER BY count DESC
    """
    )
    suspend fun getModelCountByProvider(status: Int = Status.ACTIVE): Map<Long, Int>

    /**
     * 获取各家族的模型数量统计
     */
    @Query(
        """
        SELECT family, COUNT(*) as count
        FROM llm_models
        WHERE family IS NOT NULL AND status = :status
        GROUP BY family
        ORDER BY count DESC
    """
    )
    suspend fun getModelCountByFamily(status: Int = Status.ACTIVE): Map<String, Int>

    /**
     * 获取有定价信息的模型
     */
    @Query(
        """
        SELECT * FROM llm_models
        WHERE (input_tokens IS NOT NULL OR output_tokens IS NOT NULL OR total_tokens IS NOT NULL)
           AND status = :status
        ORDER BY COALESCE(input_tokens, output_tokens, total_tokens) ASC
    """
    )
    fun getModelsWithPricing(status: Int = Status.ACTIVE): Flow<List<LlmModel>>

    /**
     * 获取指定Token限制范围内的模型
     */
    @Query(
        """
        SELECT * FROM llm_models
        WHERE max_tokens IS NOT NULL
           AND max_tokens BETWEEN :minTokens AND :maxTokens
           AND status = :status
        ORDER BY max_tokens ASC
    """
    )
    fun getModelsByTokenRange(
        minTokens: Int, maxTokens: Int, status: Int = Status.ACTIVE
    ): Flow<List<LlmModel>>

    /**
     * 清空所有模型数据
     */
    @Query("DELETE FROM llm_models")
    suspend fun deleteAllModels()
}
