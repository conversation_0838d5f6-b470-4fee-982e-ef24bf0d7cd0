package com.chatbot.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.chatbot.data.database.common.Status
import com.chatbot.data.database.entities.LlmProvider
import kotlinx.coroutines.flow.Flow

/**
 * LLM提供商数据访问对象
 *
 * 提供对llm_providers表的完整CRUD操作
 */
@Dao
interface LlmProviderDao {

    /**
     * 获取所有提供商（响应式）
     */
    @Query("SELECT * FROM llm_providers ORDER BY created_at DESC")
    fun getAllProviders(): Flow<List<LlmProvider>>

    /**
     * 获取所有活跃的提供商（响应式）
     */
    @Query("SELECT * FROM llm_providers WHERE status = :status ORDER BY name ASC")
    fun getActiveProviders(status: Int = Status.ACTIVE): Flow<List<LlmProvider>>

    /**
     * 根据ID获取提供商（响应式）
     */
    @Query("SELECT * FROM llm_providers WHERE id = :id")
    fun getProviderById(id: Long): Flow<LlmProvider?>

    /**
     * 根据ID获取提供商（一次性）
     */
    @Query("SELECT * FROM llm_providers WHERE id = :id")
    suspend fun getProviderByIdSync(id: Long): LlmProvider?

    /**
     * 根据名称获取提供商
     */
    @Query("SELECT * FROM llm_providers WHERE name = :name")
    suspend fun getProviderByName(name: String): LlmProvider?

    /**
     * 根据提供商类型获取提供商列表
     */
    @Query("SELECT * FROM llm_providers WHERE provider_type = :providerType ORDER BY name ASC")
    fun getProvidersByType(providerType: String): Flow<List<LlmProvider>>

    /**
     * 搜索提供商（按名称或描述）
     */
    @Query(
        """
        SELECT * FROM llm_providers
        WHERE name LIKE '%' || :query || '%'
           OR description LIKE '%' || :query || '%'
           OR provider_type LIKE '%' || :query || '%'
        ORDER BY name ASC
    """
    )
    fun searchProviders(query: String): Flow<List<LlmProvider>>

    /**
     * 插入提供商
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProvider(provider: LlmProvider): Long

    /**
     * 批量插入提供商
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProviders(providers: List<LlmProvider>): List<Long>

    /**
     * 更新提供商
     */
    @Update
    suspend fun updateProvider(provider: LlmProvider)

    /**
     * 批量更新提供商
     */
    @Update
    suspend fun updateProviders(providers: List<LlmProvider>)

    /**
     * 删除提供商
     */
    @Delete
    suspend fun deleteProvider(provider: LlmProvider)

    /**
     * 根据ID删除提供商
     */
    @Query("DELETE FROM llm_providers WHERE id = :id")
    suspend fun deleteProviderById(id: Long)

    /**
     * 批量删除提供商
     */
    @Query("DELETE FROM llm_providers WHERE id IN (:ids)")
    suspend fun deleteProvidersByIds(ids: List<Long>)

    /**
     * 更新提供商状态
     */
    @Query("UPDATE llm_providers SET status = :status, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateProviderStatus(id: Long, status: Int, updatedAt: Long)

    /**
     * 更新提供商API密钥
     */
    @Query("UPDATE llm_providers SET api_key = :apiKey, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateProviderApiKey(id: Long, apiKey: String?, updatedAt: Long)

    /**
     * 更新提供商基础URL
     */
    @Query("UPDATE llm_providers SET base_url = :baseUrl, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateProviderBaseUrl(id: Long, baseUrl: String?, updatedAt: Long)

    /**
     * 更新提供商配置
     */
    @Query(
        """
        UPDATE llm_providers
        SET api_key = :apiKey,
            base_url = :baseUrl,
            proxy_url = :proxyUrl,
            timeout_seconds = :timeoutSeconds,
            max_retries = :maxRetries,
            custom_headers = :customHeaders,
            updated_at = :updatedAt
        WHERE id = :id
    """
    )
    suspend fun updateProviderConfig(
        id: Long,
        apiKey: String?,
        baseUrl: String?,
        proxyUrl: String?,
        timeoutSeconds: Int,
        maxRetries: Int,
        customHeaders: String?,
        updatedAt: Long
    )

    /**
     * 检查提供商名称是否已存在（排除指定ID）
     */
    @Query("SELECT COUNT(*) FROM llm_providers WHERE name = :name AND id != :excludeId")
    suspend fun isProviderNameExists(name: String, excludeId: Long = -1): Int

    /**
     * 获取提供商数量统计
     */
    @Query("SELECT COUNT(*) FROM llm_providers")
    suspend fun getProviderCount(): Int

    /**
     * 获取活跃提供商数量
     */
    @Query("SELECT COUNT(*) FROM llm_providers WHERE status = :status")
    suspend fun getActiveProviderCount(status: Int = Status.ACTIVE): Int

    /**
     * 获取各类型提供商数量统计
     */
    @Query(
        """
        SELECT provider_type, COUNT(*) as count
        FROM llm_providers
        WHERE status = :status
        GROUP BY provider_type
        ORDER BY count DESC
    """
    )
    suspend fun getProviderCountByType(status: Int = Status.ACTIVE): Map<String, Int>

    /**
     * 清空所有提供商数据
     */
    @Query("DELETE FROM llm_providers")
    suspend fun deleteAllProviders()
}