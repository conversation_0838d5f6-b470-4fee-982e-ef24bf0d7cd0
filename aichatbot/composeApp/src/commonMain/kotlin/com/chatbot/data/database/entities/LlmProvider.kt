package com.chatbot.data.database.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.chatbot.core.utils.TimeUtils
import com.chatbot.data.database.common.Status
import kotlinx.serialization.Serializable

/**
 * LLM提供商实体类
 *
 * 对应数据库表：llm_providers
 * 存储LLM服务提供商的基本信息和配置
 */
@Entity(
    tableName = "llm_providers",
    indices = [
        Index(value = ["name"], unique = true),
        Index(value = ["provider_type", "status"]),
        Index(value = ["status"])
    ]
)
@Serializable
data class LlmProvider(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,

    @ColumnInfo(name = "name")
    val name: String,

    @ColumnInfo(name = "provider_type")
    val providerType: String,

    @ColumnInfo(name = "description")
    val description: String? = null,

    @ColumnInfo(name = "icon")
    val icon: String? = null,

    // API配置信息
    @ColumnInfo(name = "api_key")
    val apiKey: String? = null,

    @ColumnInfo(name = "base_url")
    val baseUrl: String? = null,

    @ColumnInfo(name = "proxy_url")
    val proxyUrl: String? = null,

    @ColumnInfo(name = "timeout_seconds")
    val timeoutSeconds: Int = 60,

    @ColumnInfo(name = "max_retries")
    val maxRetries: Int = 3,

    @ColumnInfo(name = "custom_headers")
    val customHeaders: String? = null, // JSON string

    // 状态和管理
    @ColumnInfo(name = "status")
    val status: Int = Status.ACTIVE,

    @ColumnInfo(name = "created_at")
    val createdAt: Long = TimeUtils.currentTimestamp(),

    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = TimeUtils.currentTimestamp()
) {

    /**
     * 检查提供商是否处于活跃状态
     */
    val isActive: Boolean
        get() = Status.toBoolean(status)


}

/**
 * LLM提供商类型枚举定义
 */
enum class ProviderType(val value: String) {
    OPENAI("openai"),
    OPENAI_COMPATIBLE("openai_compatible"),
    GEMINI("gemini"),
    ANTHROPIC("anthropic"),
    CLAUDE("claude");

    companion object {
        /**
         * 根据字符串值获取枚举
         */
        fun fromString(value: String): ProviderType? {
            return entries.find { it.value == value }
        }

        /**
         * 获取所有支持的提供商类型列表
         */
        val SUPPORTED_TYPES = entries.map { it.value }

        /**
         * 检查提供商类型是否有效
         */
        fun isValidType(type: String): Boolean {
            return fromString(type) != null
        }
    }
}


