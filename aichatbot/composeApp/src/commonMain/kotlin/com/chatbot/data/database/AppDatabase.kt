package com.chatbot.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.driver.bundled.BundledSQLiteDriver
import com.chatbot.data.database.converters.DatabaseConverters
import com.chatbot.data.database.dao.LlmModelDao
import com.chatbot.data.database.dao.LlmProviderDao
import com.chatbot.data.database.entities.LlmModelEntity
import com.chatbot.data.database.entities.LlmProviderEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO

/**
 * 应用主数据库
 *
 * 管理LLM提供商和模型数据的Room数据库
 * 使用SQLite作为底层存储引擎
 */
@Database(
    entities = [
        LlmProviderEntity::class,
        LlmModelEntity::class
    ],
    version = 1,
    exportSchema = true
)
@TypeConverters(DatabaseConverters::class)
abstract class AppDatabase : RoomDatabase() {

    /**
     * 获取LLM提供商DAO
     */
    abstract fun llmProviderDao(): LlmProviderDao

    /**
     * 获取LLM模型DAO
     */
    abstract fun llmModelDao(): LlmModelDao

    companion object {
        const val DATABASE_NAME = "app_database.db"

        /**
         * 数据库迁移路径
         * 在未来版本升级时添加相应的Migration对象
         */
        val MIGRATIONS: Array<Migration> = arrayOf(
            // 示例迁移（当前不需要，因为是第一个版本）
            // MIGRATION_1_2,
            // MIGRATION_2_3
        )
    }
}

/**
 * 创建数据库实例的扩展函数
 * 每个平台需要实现具体的创建逻辑
 */
expect fun getDatabaseBuilder(): RoomDatabase.Builder<AppDatabase>

/**
 * 获取数据库实例
 * 使用单例模式确保全局唯一
 */
fun getDatabase(): AppDatabase {
    return getDatabaseBuilder()
        .setDriver(BundledSQLiteDriver())
        .setQueryCoroutineContext(Dispatchers.IO)
        .addMigrations(*AppDatabase.MIGRATIONS)
        .fallbackToDestructiveMigration() // 开发阶段使用，生产环境应移除
        .build()
}

/**
 * 数据库初始化扩展
 */
suspend fun AppDatabase.initializeDatabase() {
    // 执行数据库初始化逻辑
    // 例如：插入默认数据、执行维护任务等

    // 启用外键约束
    openHelper.writableDatabase.execSQL("PRAGMA foreign_keys = ON")

    // 设置性能优化参数
    openHelper.writableDatabase.execSQL("PRAGMA synchronous = NORMAL")
    openHelper.writableDatabase.execSQL("PRAGMA cache_size = -64000") // 64MB cache
    openHelper.writableDatabase.execSQL("PRAGMA temp_store = MEMORY")
    openHelper.writableDatabase.execSQL("PRAGMA mmap_size = 268435456") // 256MB mmap
}

// 以下是未来可能需要的数据库迁移示例
/*
val MIGRATION_1_2 = object : Migration(1, 2) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 添加新列的示例
        database.execSQL("ALTER TABLE llm_providers ADD COLUMN new_field TEXT")
    }
}

val MIGRATION_2_3 = object : Migration(2, 3) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建新表的示例
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS new_table (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                created_at TEXT NOT NULL
            )
        """)
    }
}
*/

/**
 * 数据库维护工具
 */
object DatabaseMaintenance {

    /**
     * 清理过期数据
     */
    suspend fun cleanupExpiredData(database: AppDatabase) {
        // 实现数据清理逻辑
        // 例如：删除过期的缓存数据、清理临时文件等
    }

    /**
     * 优化数据库
     */
    suspend fun optimizeDatabase(database: AppDatabase) {
        // 执行数据库优化
        database.openHelper.writableDatabase.execSQL("VACUUM")
        database.openHelper.writableDatabase.execSQL("ANALYZE")
    }

    /**
     * 检查数据库完整性
     */
    suspend fun checkDatabaseIntegrity(database: AppDatabase): Boolean {
        return try {
            val result = database.openHelper.readableDatabase.rawQuery(
                "PRAGMA integrity_check",
                emptyArray()
            )
            result.use { cursor ->
                cursor.moveToFirst()
                val status = cursor.getString(0)
                status == "ok"
            }
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取数据库统计信息
     */
    suspend fun getDatabaseStats(database: AppDatabase): DatabaseStats {
        val providerCount = database.llmProviderDao().getProviderCount()
        val modelCount = database.llmModelDao().getModelCount()
        val activeProviderCount = database.llmProviderDao().getActiveProviderCount()
        val activeModelCount = database.llmModelDao().getActiveModelCount()

        return DatabaseStats(
            totalProviders = providerCount,
            activeProviders = activeProviderCount,
            totalModels = modelCount,
            activeModels = activeModelCount
        )
    }
}

/**
 * 数据库统计信息数据类
 */
data class DatabaseStats(
    val totalProviders: Int,
    val activeProviders: Int,
    val totalModels: Int,
    val activeModels: Int
)