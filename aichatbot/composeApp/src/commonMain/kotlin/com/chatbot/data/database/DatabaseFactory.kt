package com.chatbot.data.database

import kotlinx.coroutines.InternalCoroutinesApi
import kotlinx.coroutines.internal.synchronized

/**
 * 数据库工厂类
 * 提供统一的数据库实例获取方法
 */
object DatabaseFactory {

    private var database: AppDatabase? = null

    /**
     * 获取数据库实例（单例模式）
     */
    @OptIn(InternalCoroutinesApi::class)
    fun getDatabase(): AppDatabase {
        return database ?: synchronized(this) {
            database ?: getDatabaseBuilder().build().also { database = it }
        }
    }

}