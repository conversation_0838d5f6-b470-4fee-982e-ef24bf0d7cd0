package com.chatbot.data.repositories

import com.chatbot.core.utils.TimeUtils
import com.chatbot.data.database.common.Status.ACTIVE
import com.chatbot.data.database.common.Status.INACTIVE
import com.chatbot.data.database.dao.LlmProviderDao
import com.chatbot.data.database.entities.LlmProvider
import com.chatbot.data.database.entities.ProviderType
import kotlinx.coroutines.flow.Flow

/**
 * LLM提供商仓库
 *
 * 提供对LLM提供商数据的高级访问接口，实现Repository模式
 * 封装数据访问逻辑，提供清晰的业务接口
 */
class LlmProviderRepository(
    private val providerDao: LlmProviderDao
) {

    /**
     * 获取所有提供商（响应式）
     */
    fun getAllProviders(): Flow<List<LlmProvider>> {
        return providerDao.getAllProviders()
    }

    /**
     * 获取所有活跃的提供商（响应式）
     */
    fun getActiveProviders(): Flow<List<LlmProvider>> {
        return providerDao.getActiveProviders()
    }

    /**
     * 根据ID获取提供商（响应式）
     */
    fun getProviderById(id: Long): Flow<LlmProvider?> {
        return providerDao.getProviderById(id)
    }

    /**
     * 根据ID获取提供商（一次性）
     */
    suspend fun getProviderByIdSync(id: Long): LlmProvider? {
        return providerDao.getProviderByIdSync(id)
    }

    /**
     * 根据名称获取提供商
     */
    suspend fun getProviderByName(name: String): LlmProvider? {
        return providerDao.getProviderByName(name)
    }

    /**
     * 根据提供商类型获取提供商列表
     */
    fun getProvidersByType(providerType: String): Flow<List<LlmProvider>> {
        return providerDao.getProvidersByType(providerType)
    }

    /**
     * 搜索提供商
     */
    fun searchProviders(query: String): Flow<List<LlmProvider>> {
        return providerDao.searchProviders(query)
    }

    /**
     * 创建新的提供商
     */
    suspend fun createProvider(
        name: String,
        providerType: String,
        description: String? = null,
        icon: String? = null,
        apiKey: String? = null,
        baseUrl: String? = null,
        proxyUrl: String? = null,
        timeoutSeconds: Int = 60,
        maxRetries: Int = 3,
        customHeaders: String? = null
    ): Result<Long> {
        return try {
            // 检查名称是否已存在
            if (isProviderNameExists(name)) {
                return Result.failure(Exception("提供商名称已存在"))
            }

            // 验证提供商类型
            if (providerType in ProviderType.SUPPORTED_TYPES) {
                return Result.failure(Exception("不支持的提供商类型"))
            }

            val provider = LlmProvider(
                name = name,
                providerType = providerType,
                description = description,
                icon = icon,
                apiKey = apiKey,
                baseUrl = baseUrl,
                proxyUrl = proxyUrl,
                timeoutSeconds = timeoutSeconds,
                maxRetries = maxRetries,
                customHeaders = customHeaders,
                status = ACTIVE,
                createdAt = TimeUtils.currentTimestamp(),
                updatedAt = TimeUtils.currentTimestamp()
            )

            val id = providerDao.insertProvider(provider)
            Result.success(id)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 更新提供商信息
     */
    suspend fun updateProvider(
        id: Long,
        name: String? = null,
        description: String? = null,
        icon: String? = null,
        apiKey: String? = null,
        baseUrl: String? = null,
        proxyUrl: String? = null,
        timeoutSeconds: Int? = null,
        maxRetries: Int? = null,
        customHeaders: String? = null
    ): Result<Unit> {
        return try {
            val existingProvider = providerDao.getProviderByIdSync(id)
                ?: return Result.failure(Exception("提供商不存在"))

            // 如果名称有变化，检查新名称是否已存在
            if (name != null && name != existingProvider.name) {
                if (isProviderNameExists(name, excludeId = id)) {
                    return Result.failure(Exception("提供商名称已存在"))
                }
            }

            val updatedProvider = existingProvider.copy(
                name = name ?: existingProvider.name,
                description = description ?: existingProvider.description,
                icon = icon ?: existingProvider.icon,
                apiKey = apiKey ?: existingProvider.apiKey,
                baseUrl = baseUrl ?: existingProvider.baseUrl,
                proxyUrl = proxyUrl ?: existingProvider.proxyUrl,
                timeoutSeconds = timeoutSeconds ?: existingProvider.timeoutSeconds,
                maxRetries = maxRetries ?: existingProvider.maxRetries,
                customHeaders = customHeaders ?: existingProvider.customHeaders,
                updatedAt = TimeUtils.currentTimestamp()
            )

            providerDao.updateProvider(updatedProvider)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 更新提供商配置
     */
    suspend fun updateProviderConfig(
        id: Long,
        apiKey: String? = null,
        baseUrl: String? = null,
        proxyUrl: String? = null,
        timeoutSeconds: Int? = null,
        maxRetries: Int? = null,
        customHeaders: String? = null
    ): Result<Unit> {
        return try {
            val existingProvider = providerDao.getProviderByIdSync(id)
                ?: return Result.failure(Exception("提供商不存在"))

            providerDao.updateProviderConfig(
                id = id,
                apiKey = apiKey ?: existingProvider.apiKey,
                baseUrl = baseUrl ?: existingProvider.baseUrl,
                proxyUrl = proxyUrl ?: existingProvider.proxyUrl,
                timeoutSeconds = timeoutSeconds ?: existingProvider.timeoutSeconds,
                maxRetries = maxRetries ?: existingProvider.maxRetries,
                customHeaders = customHeaders ?: existingProvider.customHeaders,
                updatedAt = TimeUtils.currentTimestamp()
            )
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 激活提供商
     */
    suspend fun activateProvider(id: Long): Result<Unit> {
        return updateProviderStatus(id, ACTIVE)
    }

    /**
     * 停用提供商
     */
    suspend fun deactivateProvider(id: Long): Result<Unit> {
        return updateProviderStatus(id, INACTIVE)
    }

    /**
     * 更新提供商状态
     */
    private suspend fun updateProviderStatus(id: Long, status: Int): Result<Unit> {
        return try {
            providerDao.updateProviderStatus(
                id = id,
                status = status,
                updatedAt = TimeUtils.currentTimestamp()
            )
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 删除提供商
     */
    suspend fun deleteProvider(id: Long): Result<Unit> {
        return try {
            providerDao.deleteProviderById(id)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 批量删除提供商
     */
    suspend fun deleteProviders(ids: List<Long>): Result<Unit> {
        return try {
            providerDao.deleteProvidersByIds(ids)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 检查提供商名称是否已存在
     */
    private suspend fun isProviderNameExists(name: String, excludeId: Long = -1): Boolean {
        return providerDao.isProviderNameExists(name, excludeId) > 0
    }

    /**
     * 获取提供商数量统计
     */
    suspend fun getProviderCount(): Int {
        return providerDao.getProviderCount()
    }

    /**
     * 获取活跃提供商数量
     */
    suspend fun getActiveProviderCount(): Int {
        return providerDao.getActiveProviderCount()
    }

    /**
     * 获取各类型提供商数量统计
     */
    suspend fun getProviderCountByType(): Map<String, Int> {
        return providerDao.getProviderCountByType()
    }

    /**
     * 清空所有提供商数据
     */
    suspend fun clearAllProviders(): Result<Unit> {
        return try {
            providerDao.deleteAllProviders()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

}

