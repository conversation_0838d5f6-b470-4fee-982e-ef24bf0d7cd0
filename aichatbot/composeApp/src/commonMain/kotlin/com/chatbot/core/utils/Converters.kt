package com.chatbot.core.utils

import androidx.room.TypeConverter
import com.chatbot.data.database.common.Status
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.builtins.MapSerializer
import kotlinx.serialization.builtins.serializer
import kotlinx.serialization.json.Json

/**
 * Room数据库类型转换器
 *
 * 用于在Kotlin对象和数据库存储格式之间进行转换
 */
class Converters {

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }

    /**
     * 字符串列表与JSON字符串的转换
     */
    @TypeConverter
    fun fromStringList(value: List<String>?): String? {
        return if (value == null) {
            null
        } else {
            json.encodeToString(ListSerializer(String.Companion.serializer()), value)
        }
    }

    @TypeConverter
    fun toStringList(value: String?): List<String>? {
        return if (value.isNullOrBlank()) {
            null
        } else {
            try {
                json.decodeFromString(ListSerializer(String.serializer()), value)
            } catch (e: Exception) {
                null
            }
        }
    }

    /**
     * 字符串Map与JSON字符串的转换
     * 用于存储自定义请求头等键值对数据
     */
    @TypeConverter
    fun fromStringMap(value: Map<String, String>?): String? {
        return if (value == null) {
            null
        } else {
            json.encodeToString(
                MapSerializer(String.serializer(), String.serializer()),
                value
            )
        }
    }

    @TypeConverter
    fun toStringMap(value: String?): Map<String, String>? {
        return if (value.isNullOrBlank()) {
            null
        } else {
            try {
                json.decodeFromString(
                    MapSerializer(String.serializer(), String.serializer()),
                    value
                )
            } catch (e: Exception) {
                null
            }
        }
    }

    /**
     * 模型能力列表与JSON字符串的转换
     */
    @TypeConverter
    fun fromCapabilitiesList(value: List<String>?): String {
        return if (value.isNullOrEmpty()) {
            "[]"
        } else {
            json.encodeToString(ListSerializer(String.serializer()), value)
        }
    }

    @TypeConverter
    fun toCapabilitiesList(value: String?): List<String> {
        return if (value.isNullOrBlank()) {
            emptyList()
        } else {
            try {
                json.decodeFromString(ListSerializer(String.serializer()), value)
            } catch (e: Exception) {
                emptyList()
            }
        }
    }

    /**
     * 语言代码列表与JSON字符串的转换
     */
    @TypeConverter
    fun fromLanguagesList(value: List<String>?): String? {
        return if (value.isNullOrEmpty()) {
            null
        } else {
            json.encodeToString(ListSerializer(String.serializer()), value)
        }
    }

    @TypeConverter
    fun toLanguagesList(value: String?): List<String>? {
        return if (value.isNullOrBlank()) {
            null
        } else {
            try {
                json.decodeFromString(ListSerializer(String.serializer()), value)
            } catch (e: Exception) {
                null
            }
        }
    }

    /**
     * Long与String的转换（用于ID字段）
     */
    @TypeConverter
    fun fromLong(value: Long?): String? {
        return value?.toString()
    }

    @TypeConverter
    fun toLong(value: String?): Long? {
        return value?.toLongOrNull()
    }

    /**
     * Double与String的转换（用于精确的价格字段）
     */
    @TypeConverter
    fun fromDouble(value: Double?): String? {
        return value?.toString()
    }

    @TypeConverter
    fun toDouble(value: String?): Double? {
        return value?.toDoubleOrNull()
    }

    /**
     * Boolean与Int的转换（SQLite标准）
     * 使用DatabaseConstants中的状态常量
     */
    @TypeConverter
    fun fromBoolean(value: Boolean?): Int? {
        return when (value) {
            true ->Status.ACTIVE
            false ->Status.INACTIVE
            null -> null
        }
    }

    @TypeConverter
    fun toBoolean(value: Int?): Boolean? {
        return when (value) {
           Status.ACTIVE -> true
           Status.INACTIVE -> false
            else -> null
        }
    }

    /**
     * 状态转换的便捷方法
     */
    @TypeConverter
    fun fromStatus(isActive: Boolean): Int {
        return Status.fromBoolean(isActive)
    }

    @TypeConverter
    fun toStatus(status: Int): Boolean {
        return Status.toBoolean(status)
    }

    /**
     * 通用对象与JSON字符串的转换（用于复杂对象）
     */
    @TypeConverter
    fun fromAnyMap(value: Map<String, Any>?): String? {
        return if (value == null) {
            null
        } else {
            try {
                json.encodeToString(
                    MapSerializer(String.serializer(), String.serializer()),
                    value.mapValues { it.value.toString() }
                )
            } catch (e: Exception) {
                null
            }
        }
    }

    @TypeConverter
    fun toAnyMap(value: String?): Map<String, Any>? {
        return if (value.isNullOrBlank()) {
            null
        } else {
            try {
                json.decodeFromString(
                    MapSerializer(String.serializer(), String.serializer()),
                    value
                )
            } catch (e: Exception) {
                null
            }
        }
    }
}