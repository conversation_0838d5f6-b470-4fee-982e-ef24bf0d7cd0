package com.chatbot.core.utils

import kotlin.time.Clock
import kotlin.time.ExperimentalTime

/**
 * 时间工具类
 * 提供通用的时间相关操作
 */
object TimeUtils {

    /**
     * 获取当前时间戳（毫秒）
     */
    @OptIn(ExperimentalTime::class)
    fun currentTimestamp(): Long = Clock.System.now().toEpochMilliseconds()
    /**
     * 获取当前时间戳（秒）
     */
    @OptIn(ExperimentalTime::class)
    fun currentTimestampSeconds(): Long = Clock.System.now().epochSeconds
}