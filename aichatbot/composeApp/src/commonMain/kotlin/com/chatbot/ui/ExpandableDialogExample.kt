package com.chatbot.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import com.drna.shadcn.compose.component.Button
import com.drna.shadcn.compose.component.ButtonVariant
import com.drna.shadcn.compose.component.DialogAction
import com.drna.shadcn.compose.component.DialogCancel

/**
 * 基于 shadcn Dialog 的可展开对话框使用示例
 * 展示了如何在现有 shadcn Dialog 基础上添加折叠功能
 */
@Composable
fun ExpandableDialogExample() {
    var showDialog by remember { mutableStateOf(false) }
    var showSimpleDialog by remember { mutableStateOf(false) }
    var expandedState by remember { mutableStateOf(false) }

    // 设置状态
    var settings by remember {
        mutableStateOf(
            AppSettings(
                theme = "auto",
                language = "zh",
                enableNotifications = true,
                autoSave = true,
                saveInterval = 5,
                fontSize = "medium",
                enableAnimation = true,
                enableSound = false,
                debugMode = false
            )
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "基于 shadcn Dialog 的可展开对话框示例",
            style = MaterialTheme.typography.headlineSmall
        )

        if (expandedState) {
            Text(
                "高级设置状态: 已展开",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary
            )
        }

        // 完整功能示例
        Button(
            onClick = { showDialog = true },
            variant = ButtonVariant.Default
        ) {
            Icon(
                imageVector = Icons.Default.Settings,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("应用设置（完整版）")
        }

        // 简单使用示例
        Button(
            onClick = { showSimpleDialog = true },
            variant = ButtonVariant.Outline
        ) {
            Text("快速设置（简化版）")
        }
    }

    // 完整功能的可展开对话框
    ExpandableDialog(
        open = showDialog,
        onDismissRequest = { showDialog = false },
        title = { Text("应用设置") },
        description = { Text("自定义您的应用体验和偏好设置") },
        content = {
            // 基本设置内容
            BasicSettingsSection(
                settings = settings,
                onSettingsChange = { settings = it }
            )
        },
        expandableContent = {
            // 高级设置内容
            AdvancedSettingsSection(
                settings = settings,
                onSettingsChange = { settings = it }
            )
        },
        actions = {
            DialogCancel(onClick = { showDialog = false }) {
                Text("取消")
            }

            DialogAction(onClick = {
                // 保存设置逻辑
                showDialog = false
            }) {
                Text("保存设置")
            }
        },
        expandActions = {
            // 展开区域的额外操作
            Button(
                onClick = { settings = AppSettings() }, // 重置高级设置
                variant = ButtonVariant.Ghost
            ) {
                Text("重置高级设置")
            }
        },
        expandButtonText = "高级选项",
        collapseButtonText = "收起选项",
        onExpandedChange = { expandedState = it }
    )

    // 使用便捷函数的简化对话框
    ShowExpandableDialog(
        open = showSimpleDialog,
        onDismissRequest = { showSimpleDialog = false },
        title = "快速设置",
        description = "常用设置的快速访问",
        content = {
            QuickSettingsContent(
                settings = settings,
                onSettingsChange = { settings = it }
            )
        },
        expandableContent = {
            Text(
                "这里可以放置额外的高级设置选项...",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        },
        onConfirm = { showSimpleDialog = false },
        onCancel = { showSimpleDialog = false },
        expandButtonText = "更多选项",
        collapseButtonText = "收起选项"
    )
}

/**
 * 基本设置区域
 */
@Composable
private fun BasicSettingsSection(
    settings: AppSettings,
    onSettingsChange: (AppSettings) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "基本设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 主题选择
        Column {
            Text(
                text = "主题",
                style = MaterialTheme.typography.titleSmall
            )
            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.selectableGroup(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                listOf(
                    "light" to "浅色",
                    "dark" to "深色",
                    "auto" to "自动"
                ).forEach { (value, label) ->
                    FilterChip(
                        selected = settings.theme == value,
                        onClick = { onSettingsChange(settings.copy(theme = value)) },
                        label = { Text(label) }
                    )
                }
            }
        }

        // 通知开关
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "推送通知",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "接收应用通知和更新",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            Switch(
                checked = settings.enableNotifications,
                onCheckedChange = { onSettingsChange(settings.copy(enableNotifications = it)) }
            )
        }

        // 字体大小
        Column {
            Text(
                text = "字体大小",
                style = MaterialTheme.typography.titleSmall
            )
            Spacer(modifier = Modifier.height(8.dp))

            Column(modifier = Modifier.selectableGroup()) {
                listOf(
                    "small" to "小",
                    "medium" to "中等",
                    "large" to "大"
                ).forEach { (value, label) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = settings.fontSize == value,
                                onClick = { onSettingsChange(settings.copy(fontSize = value)) },
                                role = Role.RadioButton
                            )
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = settings.fontSize == value,
                            onClick = null
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(label)
                    }
                }
            }
        }
    }
}

/**
 * 高级设置区域
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AdvancedSettingsSection(
    settings: AppSettings,
    onSettingsChange: (AppSettings) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "高级设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 性能设置
        Column {
            Text(
                text = "性能",
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(8.dp))

            // 启用动画
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("启用动画效果")
                Switch(
                    checked = settings.enableAnimation,
                    onCheckedChange = { onSettingsChange(settings.copy(enableAnimation = it)) }
                )
            }

            // 自动保存
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text("自动保存")
                    Text(
                        "定期保存您的更改",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                Switch(
                    checked = settings.autoSave,
                    onCheckedChange = { onSettingsChange(settings.copy(autoSave = it)) }
                )
            }

            if (settings.autoSave) {
                Column {
                    Text(
                        "保存间隔: ${settings.saveInterval}分钟",
                        style = MaterialTheme.typography.bodySmall
                    )
                    Slider(
                        value = settings.saveInterval.toFloat(),
                        onValueChange = { onSettingsChange(settings.copy(saveInterval = it.toInt())) },
                        valueRange = 1f..30f,
                        steps = 28
                    )
                }
            }
        }

        HorizontalDivider()

        // 语言设置
        Column {
            Text(
                text = "语言",
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(8.dp))

            var expanded by remember { mutableStateOf(false) }

            ExposedDropdownMenuBox(
                expanded = expanded,
                onExpandedChange = { expanded = !expanded }
            ) {
                TextField(
                    value = when (settings.language) {
                        "zh" -> "中文"
                        "en" -> "English"
                        "ja" -> "日本語"
                        else -> "中文"
                    },
                    onValueChange = {},
                    readOnly = true,
                    label = { Text("选择语言") },
                    trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                    modifier = Modifier
                        .menuAnchor()
                        .fillMaxWidth()
                )

                ExposedDropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false }
                ) {
                    listOf(
                        "zh" to "中文",
                        "en" to "English",
                        "ja" to "日本語"
                    ).forEach { (value, label) ->
                        DropdownMenuItem(
                            text = { Text(label) },
                            onClick = {
                                onSettingsChange(settings.copy(language = value))
                                expanded = false
                            }
                        )
                    }
                }
            }
        }

        HorizontalDivider()

        // 开发者选项
        Column {
            Text(
                text = "开发者选项",
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text("调试模式")
                    Text(
                        "启用调试功能",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                Switch(
                    checked = settings.debugMode,
                    onCheckedChange = { onSettingsChange(settings.copy(debugMode = it)) }
                )
            }

            if (settings.debugMode) {
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Column(modifier = Modifier.padding(12.dp)) {
                        Text(
                            "⚠️ 警告",
                            style = MaterialTheme.typography.titleSmall,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Text(
                            "调试模式可能影响性能",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }
        }
    }
}

/**
 * 快速设置内容
 */
@Composable
private fun QuickSettingsContent(
    settings: AppSettings,
    onSettingsChange: (AppSettings) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "常用设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 主题快速切换
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text("深色模式")
            Switch(
                checked = settings.theme == "dark",
                onCheckedChange = {
                    onSettingsChange(settings.copy(theme = if (it) "dark" else "light"))
                }
            )
        }

        // 通知快速切换
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text("推送通知")
            Switch(
                checked = settings.enableNotifications,
                onCheckedChange = { onSettingsChange(settings.copy(enableNotifications = it)) }
            )
        }
    }
}

/**
 * 应用设置数据类
 */
data class AppSettings(
    val theme: String = "auto",
    val language: String = "zh",
    val enableNotifications: Boolean = true,
    val autoSave: Boolean = true,
    val saveInterval: Int = 5,
    val fontSize: String = "medium",
    val enableAnimation: Boolean = true,
    val enableSound: Boolean = false,
    val debugMode: Boolean = false
)